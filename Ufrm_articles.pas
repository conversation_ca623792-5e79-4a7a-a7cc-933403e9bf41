unit Ufrm_articles;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.Std<PERSON>trls,
  ToolCtrlsEh, DBGridEhToolCtrls, DynVarsEh, EhLibVCL, GridsEh, DBAxisGridsEh,
  DBGridEh, Vcl.ExtCtrls, Data.DB, Data.Win.ADODB, Vcl.Buttons, frxClass;

type
  Tfrm_articles = class(TForm)
    Button1: TButton;
    Button2: TButton;
    dsartlookup: TDataSource;
    qallstores: TADOQuery;
    dstobrok: TDataSource;
    Panel1: TPanel;
    Panel3: TPanel;
    Panel7: TPanel;
    DBGridEh8: TDBGridEh;
    Panel8: TPanel;
    Button15: TButton;
    Edit2: TEdit;
    DBGridEh33: TDBGridEh;
    BitBtn2: TBitBtn;
    BitBtn1: TBitBtn;
    Label1: TLabel;
    qstorestock: TADOQuery;
    dsstorestock: TDataSource;
    qgen2: TADOQuery;
    Edit3: TEdit;
    Button3: TButton;
    cmbmainsort: TComboBox;
    ComboBox1: TComboBox;
    qgen: TADOQuery;
    qartlookup: TADOQuery;
    Panel2: TPanel;
    DBGridEh1: TDBGridEh;
    amid: TADOTable;
    qamid: TADOQuery;
    dsamid: TDataSource;
    Panel4: TPanel;
    Button4: TButton;
    Button5: TButton;
    Button6: TButton;
    Panel5: TPanel;
    edtcomfrom: TEdit;
    cmbstorename: TComboBox;
    btnsearch: TButton;
    CheckBox1: TCheckBox;
    frx222: TfrxReport;
    edtstoreid: TEdit;
    Label2: TLabel;
    edtroom: TEdit;
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure Button2Click(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure Edit3Change(Sender: TObject);
    procedure DBGridEh33CellClick(Column: TColumnEh);
    procedure DBGridEh33KeyDown(Sender: TObject; var Key: Word;
      Shift: TShiftState);
    procedure BitBtn1Click(Sender: TObject);
    procedure BitBtn2Click(Sender: TObject);
    procedure Button3Click(Sender: TObject);
    procedure DBGridEh8KeyDown(Sender: TObject; var Key: Word;
      Shift: TShiftState);
    procedure cmbmainsortEnter(Sender: TObject);
    procedure ComboBox1Enter(Sender: TObject);
    procedure cmbmainsortChange(Sender: TObject);
    procedure ComboBox1Change(Sender: TObject);
    procedure ComboBox1KeyPress(Sender: TObject; var Key: Char);
    procedure cmbmainsortKeyPress(Sender: TObject; var Key: Char);
    procedure cmbmainsortSelect(Sender: TObject);
    procedure ComboBox1Select(Sender: TObject);
    procedure Edit3KeyPress(Sender: TObject; var Key: Char);
    procedure Button4Click(Sender: TObject);
    procedure Button5Click(Sender: TObject);
    procedure Button6Click(Sender: TObject);
    procedure DBGridEh8DblClick(Sender: TObject);
    procedure DBGridEh33DblClick(Sender: TObject);
    procedure btnsearchClick(Sender: TObject);
    procedure DBGridEh33KeyPress(Sender: TObject; var Key: Char);
    procedure FormShow(Sender: TObject);
    procedure cmbstorenameChange(Sender: TObject);
    procedure insertdata(qty: SmallInt; price: Real);
    procedure FormActivate(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }

  end;

var
  frm_articles: Tfrm_articles;
  ThisFormType, ThisFormMode: string;
  thisformid: Integer;

implementation

uses
  sharedfunctions, ufrmMain ,ubetweenstores;
// Usellform,

{$R *.dfm}

procedure Tfrm_articles.insertdata(qty: SmallInt; price: Real);
var
  x: SmallInt;
begin
  begin
    if betweenstores.tmid.RecordCount = 0 then
      betweenstores.tmid.Edit
    else
    begin
      betweenstores.tmid.First;
      while not betweenstores.tmid.Eof do
      begin
        if (betweenstores.tmid['miditemid'] = null) then
          betweenstores.tmid.Delete;
        if (betweenstores.tmid['miditemname'] = qartlookup['empname'])
        // and (betweenstores.tmid['midstore'] = cmbstorename.Text)
        then
        begin
          ShowMessage(qartlookup['empname'] + ' �����  ');
          Abort;
        end;
        betweenstores.tmid.Next;
      end;
    end;

    betweenstores.tmid.Insert;
    betweenstores.tmid['midok'] := True;
    betweenstores.tmid['miditemid'] := qartlookup['empid'];
    betweenstores.tmid['miditemname'] := qartlookup['empname'];
    betweenstores.tmid['midprice'] := 0;
    // price; // qartlookup['subartsoldprice'];
    betweenstores.tmid['mid_originalprice'] := 0;
    // qartlookup['subartsoldprice'];
    // falterbuing.tmid['midstore'] := '';
    // fselling.tmid['midstoreno'] := amid['amidstoreno'];

    betweenstores.tmid['midquantity'] := qty;
    betweenstores.tmid['miditemcost'] := 0; // qartlookup['artcost'];

    betweenstores.tmid.Post;
  end;
end;

procedure Tfrm_articles.BitBtn1Click(Sender: TObject);
begin
  close
end;

procedure Tfrm_articles.BitBtn2Click(Sender: TObject);
begin
 if edtcomfrom.Text = 'betweenstores' then
  begin
    if not frm_articles.amid.IsEmpty then
    begin
      amid.First;
      while not amid.Eof do
      begin
        if amid['amiditemid'] <> null then
        begin
          /// //////////////////
          if not betweenstores.tmid.IsEmpty then
          begin
            betweenstores.tmid.First;
            while not betweenstores.tmid.Eof do
            begin
              if (betweenstores.tmid['miditemid'] = null) then
                betweenstores.tmid.Delete;
              if (betweenstores.tmid['miditemname'] = amid['amiditemname']) and
                (betweenstores.tmid['midstore'] = amid['amidstore']) then
              begin
                ShowMessage(amid['amiditemname'] + ' ����� ��������� ');
                Abort;
              end;

              betweenstores.tmid.Next;
            end;
          end;
          /// ////////////////////////

          if betweenstores.tmid.RecordCount = 0 then
            betweenstores.tmid.Edit
          else
            betweenstores.tmid.Insert;
          betweenstores.tmid['midok'] := True;
          betweenstores.tmid['miditemid'] := amid['amiditemid'];
          betweenstores.tmid['miditemname'] := amid['amiditemname'];
          betweenstores.tmid['midprice'] := amid['amiditemcost'];
          betweenstores.tmid['mid_originalprice'] := amid['amiditemcost'];
          betweenstores.tmid['midstore'] := amid['amidstore'];
          betweenstores.tmid['midquantity'] := amid['amidquantity'];
          betweenstores.tmid['miditemcost'] := amid['amiditemcost'];
          betweenstores.tmid.Post;
        end;
        amid.Next;
      end;
      amid.Last;
      while not amid.Bof do
      begin
        amid.Delete;
        amid.Prior;
      end;
      if not amid.IsEmpty then
        amid.Delete;
      betweenstores.DBGridEh1.SumList.RecalcAll;
      if betweenstores.DBGridEh1.FieldColumns['midtot'].Footer.SumValue <> null
      then
      begin
        betweenstores.edttot.Text :=
          FloatToStr(betweenstores.DBGridEh1.FieldColumns['midtot']
          .Footer.SumValue);
      end;
    end;
  end ;

  close;
end;

procedure Tfrm_articles.btnsearchClick(Sender: TObject);
var
  str1, stserchtext: string;
begin
  theglobalsearchtext := 'artid>' + quotedstr('0');
  qartlookup.Filtered := False;

  // if cmbmainsort.Text<>'' then theglobalsearchtext := theglobalsearchtext +' and (artmainsort ='+QuotedStr(
  // cmbmainsort.Text)+')';
  //
  // begin
  // // stserchtext := '%' + stringreplace(Edit3.Text, ' ', '%',
  // // [rfReplaceAll, rfIgnoreCase]) + '%';
  // stserchtext := '%' + Edit3.Text + '%';
  //
  // theglobalsearchtext := theglobalsearchtext + ' and ((artname like ' +
  // QuotedStr(stserchtext) + ') or (artno like ' +
  // QuotedStr(stserchtext) + ') )';
  //
  // end;

  // theglobalsearchtext:=
  if cmbmainsort.Text <> '' then
    theglobalsearchtext := theglobalsearchtext + ' and artmainsort =' +
      quotedstr(cmbmainsort.Text);
  if Edit3.Text <> '' then
  begin
    stserchtext := '%' + stringreplace(Edit3.Text, ' ', '%',
      [rfReplaceAll, rfIgnoreCase]) + '%';
    theglobalsearchtext := theglobalsearchtext + ' and artname like ' +
      quotedstr(stserchtext);
  end;

  qartlookup.Filter := theglobalsearchtext;
  // QuotedStr('%' + Edit3.Text + '%') +' and artmainsort ='+QuotedStr(cmbmainsort.Text);
  qartlookup.Filtered := True;

end;

procedure Tfrm_articles.Button2Click(Sender: TObject);
begin
  close;
end;

procedure Tfrm_articles.Button3Click(Sender: TObject);
begin
  // if not qstorestock.IsEmpty then
  // begin
  // theglobalarticleid:=qartlookup['artid'];
  // theglobalarticlename:=qartlookup['artname'];
  // theglobalarticlecode:=qartlookup['artno'];
  // theglobalarticleprice:=qartlookup['subartsoldprice'];
  // theglobalartcost:=qartlookup['artcost'];
  // theglobalstoreid:=qstorestock['storeid'];
  // theglobalstorename:=qstorestock['storename'];
  // Close;
  //
  // end;
end;

procedure Tfrm_articles.Button4Click(Sender: TObject);
begin
  if not amid.IsEmpty then
    amid.Delete;

end;

procedure Tfrm_articles.Button5Click(Sender: TObject);
begin
  BitBtn2.Click;
end;

procedure Tfrm_articles.Button6Click(Sender: TObject);
begin
  if qstorestock.IsEmpty then
    Exit;
  if qstorestock['city_name'] <> branchcityname then
  begin
    ShowMessage('������� ����� �� ��� ������');
    Exit;
  end;
  /// //////////////////
  if Panel5.Caption <> qartlookup['artname'] then
  begin
    ShowMessage('���� ����� ����� ��� ��� ����� �� ������');
    Abort;
  end;
  if not amid.IsEmpty then
  begin
    amid.First;
    while not amid.Eof do
    begin
      if (amid['amiditemname'] = Panel5.Caption) and
        (qstorestock['storename'] = amid['amidstore']) then
      begin
        ShowMessage(amid['amiditemname'] + ' ����� ��������� ');
        Abort;
      end;

      amid.Next;
    end;
  end;
  /// ////////////////////////

  amid.Edit;
  amid.Append;
  amid['amiditemid'] := qartlookup['artid'];
  amid['amiditemname'] := qartlookup['artname'];
  amid['amidstore'] := qstorestock['storename'];
  amid['amidstoreno'] := qstorestock['storeid'];
  amid['amiditemcost'] := 0;
  amid['amidok'] := True;
  amid['amidquantity'] := 1;
//  amid['amidprice'] := qartlookup['subartsoldprice'];
  amid.Post;

end;

procedure Tfrm_articles.cmbmainsortChange(Sender: TObject);
begin
  btnsearch.Click;
end;

procedure Tfrm_articles.cmbmainsortEnter(Sender: TObject);
var
  conn: TADOConnection;
begin
  if cmbmainsort.Items.Count > 0 then
    Exit;

  try
    conn := TADOConnection.Create(nil);
    conn.LoginPrompt := False;
    conn.ConnectionString := theconnectionstring;
    conn.Open();
    qgen.Connection := conn;
    try
      qgen.SQL.Text :=
        'select distinct artmainsort from articles where artmainsort is not null and artmainsort<>'
        + quotedstr('') + ' order by artmainsort ';
      qgen.Open;
      if not qgen.IsEmpty then
      begin
        while not qgen.Eof do
        begin
          cmbmainsort.Items.Add(qgen['artmainsort']);
          qgen.Next;
        end;
      end;
    except
      on e: Exception do
      begin
        ShowMessage(e.Message);
      end;

    end;
  finally
    qgen.Connection := nil;
    conn.close;

  end;

end;

procedure Tfrm_articles.cmbmainsortKeyPress(Sender: TObject; var Key: Char);
begin
  Key := #0;
end;

procedure Tfrm_articles.cmbmainsortSelect(Sender: TObject);
begin
  btnsearch.Click;
end;

procedure Tfrm_articles.cmbstorenameChange(Sender: TObject);
begin
  edtstoreid.Text := '';
  if cmbstorename.Text <> '' then
  begin
    frmMain.qstorenames.Filter := 'storename=' + quotedstr(cmbstorename.Text);
    frmMain.qstorenames.Filtered := True;
    if not frmMain.qstorenames.IsEmpty then
      edtstoreid.Text := IntToStr(frmMain.qstorenames['storeid']);

  end;
end;

procedure Tfrm_articles.ComboBox1Change(Sender: TObject);
begin
  // theglobalarticleid:=0;
  // theglobalsubsort:=ComboBox1.Text;
  // Button4.Click;
end;

procedure Tfrm_articles.ComboBox1Enter(Sender: TObject);
begin
  ComboBox1.Items.Clear;
  qgen.SQL.Text :=
    'select distinct artsubsort from articles where artsubsort is not null and artsubsort<>'
    + quotedstr('') + ' order by artsubsort ';
  qgen.Open;
  if not qgen.IsEmpty then
  begin
    while not qgen.Eof do
    begin
      ComboBox1.Items.Add(qgen['artsubsort']);
      qgen.Next;
    end;
  end;

end;

procedure Tfrm_articles.ComboBox1KeyPress(Sender: TObject; var Key: Char);
begin
  Key := #0;
end;

procedure Tfrm_articles.ComboBox1Select(Sender: TObject);
begin
  // theglobalmainsort:=cmbmainsort.Text ;
  // theglobalsubsort:=ComboBox1.Text;
  // theglobalarticleid:=0;
  // Button4.Click;


  // theglobalarticlename:=qartlookup['artname'];
  // theglobalarticlecode:=qartlookup['artno'];
  // theglobalarticleprice:=qartlookup['subartsoldprice'];
  // theglobalartcost:=qartlookup['artcost'];
  // theglobalstoreid:=0;
  // theglobalstorename:='';

end;

procedure Tfrm_articles.DBGridEh33CellClick(Column: TColumnEh);
var
  conn: TADOConnection;
  ds: TDataSource;
  strqty, thepackname: string;
  realqty: Real;
  qty, thepackunits: Integer;
begin
//  if edtcomfrom.Text = 'fbuing' then
//    Exit;
//  if edtcomfrom.Text = 'falterbuing' then
//    Exit;
//  // if cmbstorename.Text<>'' then  Exit;
//
//  checkinternet;
//  conn := TADOConnection.Create(nil);
//  conn.LoginPrompt := False;
//  conn.ConnectionString := theconnectionstring;
//  conn.Open();
//  qstorestock.Connection := conn;
//  try
//    Panel5.Caption := qartlookup['artname'];
//    qstorestock.SQL.Clear;
//    qstorestock.SQL.Add
//      ('select storeid,storename,city_name ,isnull(sum(case when invd_trancode in ('
//      + quotedstr('OUT_TEMP') + ') then invd_invqty else 0 end ),0) as outtemp'
//      + ' ,isnull(sum(case when invd_trancode in (' + quotedstr('IN_TEMP') +
//      ') then invd_invqty else 0 end ),0) as intemp ' +
//      ', isnull(sum(case when invd_trancode in (' + quotedstr('IN_PURCHASE') +
//      ',' + quotedstr('IN_INV_OS') + ',' + quotedstr('IN_RET') + ',' +
//      quotedstr('IN_ALTER') + ',' + quotedstr('IN_STORES') + ',' +
//      quotedstr('IN_PRDCT') + ',' + quotedstr('IN_TEMP') + ') then invd_invqty '
//      + ' when  invd_trancode IN (' + quotedstr('OUT_RET') + ',' +
//      quotedstr('OUT_SALE') + ',' + quotedstr('OUT_ALTER') + ',' +
//      quotedstr('OUT_STORES') + ',' + quotedstr('OUT_PRDCT') + ',' +
//      quotedstr('OUT_TEMP') + ',' + quotedstr('OUT_DAMAGE') +
//      ') then -(invd_invqty) end  ),0) as tosell ' +
//      ', isnull(sum(case when invd_trancode in (' + quotedstr('IN_PURCHASE') +
//      ' ,' + quotedstr('IN_INV_OS') + ',' + quotedstr('IN_RET') + ',' +
//      quotedstr('IN_ALTER') + ',' + quotedstr('IN_STORES') + ', ' +
//      quotedstr('IN_PRDCT') + '  ) then invd_invqty ' +
//      ' when  invd_trancode IN (' + quotedstr('OUT_RET') + ',' +
//      quotedstr('OUT_SALE') + ',' + quotedstr('OUT_ALTER') + ',' +
//      quotedstr('OUT_STORES') + ',' + quotedstr('OUT_PRDCT') + ',' +
//      quotedstr('OUT_DAMAGE') + ')then -(invd_invqty) end),0) as inventory ' +
//      ' from  sales_inv_detail join sales_inv_main on invm_id=invd_invm_id ' +
//      ' join storenames on invd_storeid=storeid join articles on artid=invd_articleid join cities on city_id=storecityid '
//      + '  where artname=' + quotedstr(qartlookup['artname']));
//    // and artlastbuingprice>0
//    if cmbstorename.Text <> '' then
//      qstorestock.SQL.Add(' and storename=' + quotedstr(cmbstorename.Text));
//
//    // qstorestock.SQL.Add(' and storebranchno=' + QuotedStr(IntToStr(branchno)));
//
//    if CheckBox1.Checked = False then
//      qstorestock.SQL.Add('and storecityid=' +
//        quotedstr(IntToStr(branchcityid)));
//
//    qstorestock.SQL.Add
//      (' group by storeid,storename,city_name'); //
//    qstorestock.Open;
//    // Exit;
//    // if not qstorestock.IsEmpty then
//    // begin
//    // if cmbstorename.Text <> '' then
//    // begin
//    // amid.Edit;
//    // amid.Append;
//    // amid['amiditemid'] := qartlookup['artid'];
//    // amid['amiditemname'] := qartlookup['artname'];
//    // amid['amidstore'] := qstorestock['storename'];
//    // amid['amidstoreno'] := qstorestock['storeid'];
//    // amid['amiditemcost'] := 200;
//    // amid['amidok'] := True;
//    // amid['amidquantity'] := qty;
//    // amid['amidprice'] := qartlookup['subartsoldprice'];
//    // amid.Post;
//    // end;
//    //
//    // Abort;
//    // end;
//    //
//    // if qstorestock.IsEmpty then
//    // begin
//    // if messagedlg(' ��� ����� ��� ����� ' + #13 + '�� ���� ���������',
//    // mtConfirmation, [mbYes, mbNo], 0) <> mryes then
//    // begin
//    // Abort;
//    // end;
//    // end;
//    //
//    // if cmbstorename.Text = '' then
//    // begin
//    // ShowMessage('��� ������');
//    // Abort;
//    // end;
//    // begin
//    // strqty := InputBox('����� ������ ��������', '������', '');
//    // try
//    // realqty := StrToInt(strqty);
//    // except
//    // ShowMessage('������ ������� ���');
//    // Abort;
//    //
//    // end;
//    //
//    // qty := StrToInt(strqty);
//    // if qty <= 0 then
//    // begin
//    // ShowMessage('������ ��� ��� ������');
//    // Abort;
//    // end;
//    //
//    // if qty > qstorestock['tosell'] then
//    // begin
//    // if messagedlg(' ������ ���� �� ������ ' + #13 + '�� ���� ���������',
//    // mtConfirmation, [mbYes, mbNo], 0) <> mryes then
//    // begin
//    //
//    // Abort;
//    //
//    // end;
//    // end;
//    // if (edtcomfrom.Text = 'fselling') then
//    // begin
//    // if fselling.tmid.RecordCount = 0 then
//    // fselling.tmid.Edit
//    // else
//    // fselling.tmid.Insert;
//    // fselling.tmid['midok'] := True;
//    // fselling.tmid['miditemid'] := qartlookup['artid'];
//    // fselling.tmid['miditemname'] := qartlookup['artname'];
//    // fselling.tmid['midprice'] := qartlookup['subartsoldprice'];
//    // fselling.tmid['mid_originalprice'] := qartlookup['subartsoldprice'];
//    // fselling.tmid['midstore'] := cmbstorename.Text;
//    // fselling.tmid['midstoreno'] := edtstoreid.Text;
//    //
//    // fselling.tmid['midquantity'] := 0;
//    // fselling.tmid['miditemcost'] := qartlookup['artcost'];
//    //
//    // fselling.tmid.Post;
//    // end
//    // else if (edtcomfrom.Text = 'falterselling') then
//    // begin
//    // if falterselling.tmid.RecordCount = 0 then
//    // falterselling.tmid.Edit
//    // else
//    // falterselling.tmid.Insert;
//    // falterselling.tmid['midok'] := True;
//    // falterselling.tmid['miditemid'] := qartlookup['artid'];
//    // falterselling.tmid['miditemname'] := qartlookup['artname'];
//    // falterselling.tmid['midprice'] := qartlookup['subartsoldprice'];
//    // falterselling.tmid['mid_originalprice'] :=
//    // qartlookup['subartsoldprice'];
//    // falterselling.tmid['midstore'] := cmbstorename.Text;
//    // falterselling.tmid['midstoreno'] := edtstoreid.Text;
//    //
//    // falterselling.tmid['midquantity'] := 0;
//    // falterselling.tmid['miditemcost'] := qartlookup['artcost'];
//    //
//    // falterselling.tmid.Post;
//    // end;
//    //
//    // end;
//    //
//    qstorestock.Connection := nil;
//    conn.close;
//  except
//    on e: Exception do
//    begin
//      ShowMessage(e.Message);
//    end;
//  end;
end;

procedure Tfrm_articles.DBGridEh33DblClick(Sender: TObject);
var
  lastbuingprice, theprice, theartstock, theartcost, price, realqty: Real;
  thepackname, thebarcode, strqty, strprice, storetype: string;
  thepackunits, qty: Integer;
begin
  Abort;
  // if edtcomfrom.Text = 'fselling' then Exit;
  // if edtcomfrom.Text = 'falterselling' then Exit;
  // if edtcomfrom.Text = 'betweenstores' then
  // Exit;
  // if qartlookup.IsEmpty then
  // Exit;
  // begin
  // strqty := InputBox('����� ������ ��������', '������', '');
  // try
  // realqty := StrToFloat(strqty);
  // except
  // ShowMessage('������ ������� ���');
  // Exit;
  //
  // end;
  //
  // qty := StrToInt(strqty);
  // if qty <= 0 then
  // begin
  // ShowMessage('������ ��� ��� ������');
  // Exit;
  // end;
  // if (edtcomfrom.Text = 'fbuing') or (edtcomfrom.Text = 'falterbuing') then
  // begin
  // strprice := InputBox('����� �����', '�����', '');
  // if (strprice = '') or (strprice = '.') then
  // begin
  // ShowMessage('����� ������ ���');
  // Exit;
  // end;
  //
  // price := StrToFloat(strprice);
  // if price <= 0 then
  // begin
  // ShowMessage('��� �����');
  // Exit;
  // end;
  //
  // end;
  // insertdata(qty, 0);
  // Abort;
  // /// ////////
  // if (edtcomfrom.Text = 'fbuing') then
  // begin
  // if fbuing.tmid.RecordCount = 0 then
  // fbuing.tmid.Edit
  // else
  // fbuing.tmid.Insert;
  // fbuing.tmid['midok'] := True;
  // fbuing.tmid['miditemid'] := qartlookup['artid'];
  // fbuing.tmid['miditemname'] := qartlookup['artname'];
  // fbuing.tmid['midprice'] := price; // qartlookup['subartsoldprice'];
  // fbuing.tmid['mid_originalprice'] := 0; // qartlookup['subartsoldprice'];
  // fbuing.tmid['midstore'] := '';
  // // fselling.tmid['midstoreno'] := amid['amidstoreno'];
  //
  // fbuing.tmid['midquantity'] := qty;
  // fbuing.tmid['miditemcost'] := 0; // qartlookup['artcost'];
  //
  // fbuing.tmid.Post;
  // end
  // else if (edtcomfrom.Text = 'falterbuing') then
  // begin
  // if falterbuing.tmid.RecordCount = 0 then
  // falterbuing.tmid.Edit
  // else
  // falterbuing.tmid.Insert;
  // falterbuing.tmid['midok'] := True;
  // falterbuing.tmid['miditemid'] := qartlookup['artid'];
  // falterbuing.tmid['miditemname'] := qartlookup['artname'];
  // falterbuing.tmid['midprice'] := price; // qartlookup['subartsoldprice'];
  // falterbuing.tmid['mid_originalprice'] := 0;
  // // qartlookup['subartsoldprice'];
  // falterbuing.tmid['midstore'] := '';
  // // fselling.tmid['midstoreno'] := amid['amidstoreno'];
  //
  // falterbuing.tmid['midquantity'] := qty;
  // falterbuing.tmid['miditemcost'] := 0; // qartlookup['artcost'];
  //
  // falterbuing.tmid.Post;
  // end
  // else if (edtcomfrom.Text = 'fselling') then
  // begin
  // insertdata(qty, 0);
  // // if fselling.tmid.RecordCount = 0 then
  // // fselling.tmid.Edit
  // // else
  // // fselling.tmid.Insert;
  // // fselling.tmid['midok'] := True;
  // // fselling.tmid['miditemid'] := qartlookup['artid'];
  // // fselling.tmid['miditemname'] := qartlookup['artname'];
  // // fselling.tmid['midprice'] := qartlookup['subartsoldprice'];
  // // fselling.tmid['mid_originalprice'] := qartlookup['subartsoldprice'];
  // // fselling.tmid['midstore'] := cmbstorename.Text;
  // // fselling.tmid['midstoreno'] := edtstoreid.Text;
  // //
  // // fselling.tmid['midquantity'] := 0;
  // // fselling.tmid['miditemcost'] := qartlookup['artcost'];
  // //
  // // fselling.tmid.Post;
  // end
  // else if (edtcomfrom.Text = 'falterselling') then
  // begin
  // if falterselling.tmid.RecordCount = 0 then
  // falterselling.tmid.Edit
  // else
  // falterselling.tmid.Insert;
  // falterselling.tmid['midok'] := True;
  // falterselling.tmid['miditemid'] := qartlookup['artid'];
  // falterselling.tmid['miditemname'] := qartlookup['artname'];
  // falterselling.tmid['midprice'] := qartlookup['subartsoldprice'];
  // falterselling.tmid['mid_originalprice'] := qartlookup['subartsoldprice'];
  // falterselling.tmid['midstore'] := cmbstorename.Text;
  // falterselling.tmid['midstoreno'] := edtstoreid.Text;
  //
  // falterselling.tmid['midquantity'] := 0;
  // falterselling.tmid['miditemcost'] := qartlookup['artcost'];
  //
  // falterselling.tmid.Post;
  // end;
  //
  // end;

  // if not qartlookup.IsEmpty then
  // begin
  // if fselling.tmid.RecordCount = 0 then
  // fselling.tmid.Edit
  // else
  // fselling.tmid.Insert;
  // fselling.tmid['midok'] := True;
  // fselling.tmid['miditemid'] := qartlookup['artid'];
  // fselling.tmid['miditemname'] := qartlookup['artname'];
  // fselling.tmid['midprice'] := qartlookup['subartsoldprice'];
  // fselling.tmid['mid_originalprice'] := qartlookup['subartsoldprice'];
  // fselling.tmid['midstore'] := '';
  // // fselling.tmid['midstoreno'] := amid['amidstoreno'];
  //
  // fselling.tmid['midquantity'] := 1;
  // fselling.tmid['miditemcost'] := qartlookup['artcost'];
  //
  // fselling.tmid.Post;
  // end;
  //
  // fselling.DBGridEh1.SumList.RecalcAll;
  // if fselling.DBGridEh1.FieldColumns['midtot'].Footer.SumValue <> null then
  // begin
  // fselling.edttot.Text := FloatToStr(fselling.DBGridEh1.FieldColumns['midtot']
  // .Footer.SumValue);
  // if (fselling.edtdiscount.Text = '') or (fselling.edtdiscount.Text = '.')
  // then
  // fselling.edtdiscount.Text := '0';
  //
  // fselling.edtnet.Text := FloatToStr(fselling.DBGridEh1.FieldColumns['midtot']
  // .Footer.SumValue - StrToFloat(fselling.edtdiscount.Text));
  // end;
  // // fselling.ActiveControl:=fselling.edtdiscount;
  //
  // close;

end;

procedure Tfrm_articles.DBGridEh33KeyDown(Sender: TObject; var Key: Word;
  Shift: TShiftState);
begin
  // if Key = VK_RETURN then
  // begin
  //
  // BitBtn2.Click;
  //
  // end;

end;

procedure Tfrm_articles.DBGridEh33KeyPress(Sender: TObject; var Key: Char);
var
  lastbuingprice, theprice, theartstock, theartcost, price, realqty: Real;
  thepackname, thebarcode, strqty, strprice, storetype: string;
  thepackunits, qty: Integer;
begin
  if (Key = Char(VK_RETURN)) then
  begin

    // if edtcomfrom.Text = 'fselling' then Exit;
    // if edtcomfrom.Text = 'falterselling' then Exit;
    // if edtcomfrom.Text = 'betweenstores' then
    // Exit;
    if qartlookup.IsEmpty then
      Exit;

    if edtcomfrom.Text <> 'betweenstores' then
    begin
      if cmbstorename.Text = '' then
      begin
        ShowMessage('��� ������');
        Exit;
      end;
    end;

    insertdata(1, 0);
  end;

end;

procedure Tfrm_articles.DBGridEh8DblClick(Sender: TObject);
var
  lastbuingprice, theprice, theartstock, theartcost, price, realqty: Real;
  thepackname, thebarcode, strqty, strprice, storetype: string;
  thepackunits, qty: Integer;
begin
//  begin
//    if qstorestock.IsEmpty then
//      Exit;
//
//    strqty := InputBox('����� ������ ��������', '������', '');
//    try
//      realqty := StrToFloat(strqty);
//    except
//      ShowMessage('������ ������� ���');
//      Exit;
//
//    end;
//
//    qty := StrToInt(strqty);
//    if qty <= 0 then
//    begin
//      ShowMessage('������ ��� ��� ������');
//      Exit;
//    end;
////    price := qartlookup['subartsoldprice'];
//
//    if edtcomfrom.Text = 'fselling' then
//    begin
//      if qstorestock.IsEmpty then
//      begin
//        if messagedlg(' ��� ����� ������� ' + #13 + '�� ���� ������ ��������',
//          mtConfirmation, [mbYes, mbNo], 0) <> mryes then
//        begin
//          Abort;
//        end;
//      end;
//      if fselling.tmid.RecordCount = 0 then
//        fselling.tmid.Edit
//      else
//      begin
//        fselling.tmid.First;
//        while not fselling.tmid.Eof do
//        begin
//          if (fselling.tmid['miditemid'] = null) then
//            fselling.tmid.Delete;
//          if (fselling.tmid['miditemname'] = qartlookup['artname'])
//          // and  (fselling.tmid['midstore'] = cmbstorename.Text)
//          then
//          begin
//            ShowMessage(qartlookup['artname'] + ' ����� ��������� ');
//            Abort;
//          end;
//          fselling.tmid.Next;
//        end;
//      end;
//      fselling.tmid.Append;
//      fselling.tmid['midok'] := True;
//      fselling.tmid['miditemid'] := qartlookup['artid'];
//      fselling.tmid['miditemname'] := qartlookup['artname'];
//      fselling.tmid['midprice'] := qartlookup['subartsoldprice'];
//      fselling.tmid['mid_originalprice'] := qartlookup['subartsoldprice'];
//      fselling.tmid['midstore'] :=qstorestock['storename'];
//      fselling.tmid['midstoreno'] :=0;// edtstoreid.Text;
//      fselling.tmid['midquantity'] := qty;
//      fselling.tmid['miditemcost'] := qartlookup['artcost'];
//      fselling.tmid.Post;
//       fselling.DBGridEh1.SumList.RecalcAll;
//  if fselling.DBGridEh1.FieldColumns['midtot'].Footer.SumValue <> null then
//  begin
//    fselling.edttot.Text := FloatToStr(fselling.DBGridEh1.FieldColumns['midtot'].Footer.SumValue);
//    if (fselling.edtdiscount.Text = '') or (fselling.edtdiscount.Text = '.') then
//      fselling.edtdiscount.Text := '0';
//
//
//    fselling.edtnet.Text := FloatToStr(fselling.DBGridEh1.FieldColumns['midtot'].Footer.SumValue -
//      StrToFloat(fselling.edtdiscount.Text));
//  end
//  Else
//    fselling.edttot.Text := '0';
//    end
//
//  end;

end;

procedure Tfrm_articles.DBGridEh8KeyDown(Sender: TObject; var Key: Word;
  Shift: TShiftState);
begin
  Button6.Click;
end;

procedure Tfrm_articles.Edit3Change(Sender: TObject);
var
  str1, stserchtext: string;
begin
  btnsearch.Click;
  // theglobalsearchtext := '';
  // qartlookup.Filtered := False;
  // if Edit3.Text <> '' then
  // begin
  // // stserchtext := '%' + stringreplace(Edit3.Text, ' ', '%',
  // // [rfReplaceAll, rfIgnoreCase]) + '%';
  // stserchtext := '%' + Edit3.Text + '%';
  //
  // theglobalsearchtext := theglobalsearchtext + '(artname like ' +
  // QuotedStr(stserchtext) + ') or (artno like ' +
  // QuotedStr(stserchtext) + ') ';
  // end;
  // qartlookup.Filter := theglobalsearchtext;
  // qartlookup.Filtered := True;

end;

procedure Tfrm_articles.Edit3KeyPress(Sender: TObject; var Key: Char);
var
  str1, stserchtext: string;

begin
  if Key = Char(VK_RETURN) then
  begin
    theglobalsearchtext := '';
    qartlookup.Filtered := False;
    if Edit3.Text <> '' then
    begin
      // stserchtext := '%' + stringreplace(Edit3.Text, ' ', '%',
      // [rfReplaceAll, rfIgnoreCase]) + '%';
      stserchtext := '%' + Edit3.Text + '%';

      theglobalsearchtext := theglobalsearchtext + '(artname like ' +
        quotedstr(stserchtext) + ') or (artno like ' +
        quotedstr(stserchtext) + ') ';
    end;
    qartlookup.Filter := theglobalsearchtext;
    qartlookup.Filtered := True;

  end;
end;

procedure Tfrm_articles.FormActivate(Sender: TObject);
var
  conn: TADOConnection;
begin
  conn := TADOConnection.Create(nil);
  conn.LoginPrompt := False;
  conn.ConnectionString := theconnectionstring;
  conn.Open();
  qartlookup.Connection := conn;
  try



    qartlookup.SQL.Clear;
    qartlookup.SQL.Add
      ('select empid,empname from emptable t1 join sales_inv_detail t2 on empid=invd_empid ');
    qartlookup.SQL.Add(' where  empstate='+QuotedStr('�����')+
    ' and t2.invd_trancode='+QuotedStr('����') +
    ' and t2.invd_id=(select max(invd_id) from sales_inv_detail where invd_empid=t1.empid and invd_storeid='+
    QuotedStr(IntToStr(2))) ;
    if edtroom.Text<>'' then qartlookup.SQL.Add(' and invd_room='+QuotedStr(edtroom.Text));
    qartlookup.SQL.Add(')');
     qartlookup.Open;


//    frmMain.qstorenames.Filter := 'storebranchno=' +
//      quotedstr(IntToStr(branchno));
//    frmMain.qstorenames.Filtered := True;
    if not frmMain.qstorenames.IsEmpty then
    begin
      frmMain.qstorenames.First;
      while not frmMain.qstorenames.Eof do
      begin
        cmbstorename.Items.Add(frmMain.qstorenames['storename']);
        frmMain.qstorenames.Next;
      end;

    end;
    qartlookup.Connection := nil;
    conn.close;
  except
    on e: Exception do
    begin
      ShowMessage(e.Message);
    end;
  end;

end;

procedure Tfrm_articles.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  Action := caHide;
end;

procedure Tfrm_articles.FormCreate(Sender: TObject);
var
  conn: TADOConnection;
  ds: TDataSource;
var
  sqltxt1, sqltxt2, sqltxt3, sqltxt5, userlang: string;
  i: SmallInt;
var

  dsarticles: TDataSource;
begin
  // if checktheinternet=False then ;
  conn := TADOConnection.Create(nil);
  conn.LoginPrompt := False;
  conn.ConnectionString := theconnectionstring;
  conn.Open();
  amid.Connection := conn;
  qamid.Connection := conn;
  thisformid := tradformcount;

  sqltxt1 := 'CREATE TABLE [dbo].[#amidtbl' + theuserid;
  sqltxt2 := IntToStr(thisformid);
  sqltxt3 := qamid.SQL.Text + theuserid;
  sqltxt5 :=
    ']  PRIMARY KEY CLUSTERED ([amidtableid] ASC )WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY] ) ON [PRIMARY]';
  qamid.SQL.Text := sqltxt1 + sqltxt2 + sqltxt3 + sqltxt2 + sqltxt5;
  try
    qamid.ExecSQL;
  except
    on e: Exception do
    begin
      ShowMessage(e.Message);
    end;
  end;

  amid.TableName := '#amidtbl' + theuserid + IntToStr(thisformid);
  amid.Open;
  amid.Connection := nil;

end;

procedure Tfrm_articles.FormShow(Sender: TObject);
begin
//  Edit3.Text := '';
//  if edtcomfrom.Text <> 'betweenstores' then
//    cmbstorename.Text := '';
//  edtstoreid.Text := '';
//  if (edtcomfrom.Text = 'fbuing') or (edtcomfrom.Text = 'falterbuing') then
//    DBGridEh33.FieldColumns['subartsoldprice'].Visible := False
//  else
//    DBGridEh33.FieldColumns['subartsoldprice'].Visible := True;

end;

end.
