# تحديث نظام إدارة السجن - إضافة الرسم البياني للإحصائيات

## الوصف
تم إضافة رسم بياني عصري لعرض إحصائيات النزلاء في الواجهة الرئيسية (ufrmmain) عند تكبير النافذة إلى أقصى حجم.

## الميزات المضافة

### 1. الرسم البياني الدائري
- يظهر عند تكبير النافذة الرئيسية
- يعرض إحصائيات النزلاء حسب الحالة
- تصميم عصري مع ألوان مميزة لكل حالة

### 2. الحالات المعروضة
- **موجود** (أخضر): النزلاء الموجودين حالياً
- **مأمورية خروج** (أزرق): النزلاء في مأمورية خروج للعمل
- **مأمورية عيادة** (أصفر): النزلاء في مأمورية خروج للعيادة
- **إجازة** (برتقالي): النزلاء في إجازة
- **خروج بكفالة** (أحمر): النزلاء الذين خرجوا بكفالة
- **أخرى** (رمادي): الحالات الأخرى

### 3. التحديث التلقائي
- يتم تحديث البيانات كل 30 ثانية تلقائياً
- يتم استعلام قاعدة البيانات للحصول على أحدث الإحصائيات

## الملفات المعدلة

### 1. ufrmMain.pas
- إضافة مكونات الرسم البياني
- إضافة إجراءات الرسم والتحديث
- إضافة معالج تغيير حجم النافذة

### 2. ufrmMain.dfm
- إضافة مكونات Timer و ADOQuery للإحصائيات
- إضافة معالج FormResize

## التقنيات المستخدمة

### الرسم المخصص
- استخدام Canvas للرسم المباشر
- رسم دائري مخصص بدلاً من مكتبات خارجية
- مفتاح (Legend) مخصص لعرض الألوان والتسميات

### قاعدة البيانات
- استعلامات SQL محسنة للحصول على الإحصائيات
- استخدام ISNULL لضمان عدم إرجاع قيم NULL
- تجميع البيانات حسب حالة النزيل (empstate)

## كيفية الاستخدام

1. قم بتشغيل التطبيق
2. قم بتكبير النافذة الرئيسية إلى أقصى حجم
3. سيظهر الرسم البياني تلقائياً في الخلفية
4. سيتم تحديث البيانات كل 30 ثانية

## المتطلبات

- Delphi 10.2 أو أحدث
- قاعدة بيانات SQL Server مع الجداول:
  - emptable: جدول النزلاء الرئيسي
  - empstate: حقل حالة النزيل

## الاستعلامات المستخدمة

```sql
-- عدد النزلاء الموجودين
SELECT ISNULL(COUNT(empid), 0) AS cnt FROM emptable WHERE empstate = 'موجود'

-- عدد المأمورية خروج
SELECT ISNULL(COUNT(empid), 0) AS cnt FROM emptable WHERE empstate = 'مأمورية خروج'

-- عدد المأمورية عيادة
SELECT ISNULL(COUNT(empid), 0) AS cnt FROM emptable WHERE empstate = 'مأمورية عيادة'

-- عدد الإجازات
SELECT ISNULL(COUNT(empid), 0) AS cnt FROM emptable WHERE empstate = 'إجازة'

-- عدد الخروج بكفالة
SELECT ISNULL(COUNT(empid), 0) AS cnt FROM emptable WHERE empstate = 'خروج بكفالة'

-- الحالات الأخرى
SELECT ISNULL(COUNT(empid), 0) AS cnt FROM emptable 
WHERE empstate NOT IN ('موجود', 'مأمورية خروج', 'مأمورية عيادة', 'إجازة', 'خروج بكفالة')
```

## ملاحظات فنية

- الرسم البياني يستخدم الرسم المباشر على Canvas
- لا يتطلب مكتبات خارجية مثل TeeChart
- محسن للأداء مع تحديث تلقائي
- يدعم اللغة العربية بالكامل
- تصميم متجاوب مع حجم النافذة

## الدعم والصيانة

- يمكن تعديل ألوان الحالات من خلال تعديل مصفوفة StatsColors
- يمكن تغيير فترة التحديث من خلال تعديل Timer1.Interval
- يمكن إضافة حالات جديدة بتوسيع المصفوفات وإضافة استعلامات جديدة
