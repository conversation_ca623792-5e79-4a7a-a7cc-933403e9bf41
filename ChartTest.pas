unit ChartTest;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls,
  VclTee.TeeGDIPlus, VCLTee.TeEngine, VCLTee.Series, VCLTee.TeeProcs, VCLTee.Chart;

type
  TForm1 = class(TForm)
    Panel1: TPanel;
    Button1: TButton;
    Chart1: TChart;
    procedure Button1Click(Sender: TObject);
    procedure FormCreate(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  Form1: TForm1;

implementation

{$R *.dfm}

procedure TForm1.FormCreate(Sender: TObject);
var
  PieSeries: TPieSeries;
begin
  // إنشاء سلسلة الرسم البياني الدائري
  PieSeries := TPieSeries.Create(Chart1);
  PieSeries.ParentChart := Chart1;
  PieSeries.Title := 'حالات النزلاء';
  PieSeries.Circled := True;
  PieSeries.ExplodeBiggest := 5;
  
  Chart1.Title.Text.Text := 'إحصائيات النزلاء';
  Chart1.Title.Font.Size := 16;
  Chart1.Title.Font.Style := [fsBold];
  Chart1.Title.Font.Color := clNavy;
  Chart1.Color := clWhite;
  Chart1.View3D := True;
  Chart1.Legend.Visible := True;
  Chart1.Legend.Alignment := laRight;
end;

procedure TForm1.Button1Click(Sender: TObject);
var
  PieSeries: TPieSeries;
begin
  PieSeries := TPieSeries(Chart1.SeriesList[0]);
  PieSeries.Clear;
  
  // إضافة بيانات تجريبية
  PieSeries.Add(150, 'موجود (150)', clGreen);
  PieSeries.Add(25, 'مأمورية خروج (25)', clBlue);
  PieSeries.Add(10, 'مأمورية عيادة (10)', clYellow);
  PieSeries.Add(5, 'إجازة (5)', clOrange);
  PieSeries.Add(8, 'خروج بكفالة (8)', clRed);
  PieSeries.Add(2, 'أخرى (2)', clGray);
end;

end.
