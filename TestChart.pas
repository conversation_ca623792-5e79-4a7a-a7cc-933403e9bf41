unit TestChart;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls, System.Math;

type
  TChartPanel = class(TPanel)
  protected
    procedure Paint; override;
  public
    StatsData: array[0..5] of Integer;
    StatsLabels: array[0..5] of string;
    StatsColors: array[0..5] of TColor;
  end;

  TFormTest = class(TForm)
    Panel1: TPanel;
    Button1: TButton;
    procedure Button1Click(Sender: TObject);
    procedure FormCreate(Sender: TObject);
  private
    PanelChart: TChartPanel;
  public
  end;

var
  FormTest: TFormTest;

implementation

{$R *.dfm}

procedure TFormTest.FormCreate(Sender: TObject);
var
  i: Integer;
begin
  PanelChart := TChartPanel.Create(Self);
  PanelChart.Parent := Self;
  PanelChart.Align := alClient;
  PanelChart.Color := clWhite;
  PanelChart.BevelOuter := bvNone;

  // تهيئة البيانات الأولية
  for i := 0 to 5 do
  begin
    PanelChart.StatsData[i] := 0;
    PanelChart.StatsLabels[i] := '';
    PanelChart.StatsColors[i] := clWhite;
  end;

  // إضافة بيانات تجريبية أولية
  PanelChart.StatsData[0] := 150;
  PanelChart.StatsLabels[0] := 'Present (150)';
  PanelChart.StatsColors[0] := clGreen;

  PanelChart.StatsData[1] := 25;
  PanelChart.StatsLabels[1] := 'Work Assignment (25)';
  PanelChart.StatsColors[1] := clBlue;

  PanelChart.StatsData[2] := 10;
  PanelChart.StatsLabels[2] := 'Medical Assignment (10)';
  PanelChart.StatsColors[2] := clYellow;

  PanelChart.StatsData[3] := 5;
  PanelChart.StatsLabels[3] := 'Leave (5)';
  PanelChart.StatsColors[3] := $0080FF;

  PanelChart.StatsData[4] := 8;
  PanelChart.StatsLabels[4] := 'Bail Release (8)';
  PanelChart.StatsColors[4] := clRed;

  PanelChart.StatsData[5] := 2;
  PanelChart.StatsLabels[5] := 'Other (2)';
  PanelChart.StatsColors[5] := clGray;

  // تهيئة مولد الأرقام العشوائية
  Randomize;
end;

procedure TFormTest.Button1Click(Sender: TObject);
begin
  // تحديث البيانات وإعادة الرسم
  PanelChart.StatsData[0] := Random(200) + 50;
  PanelChart.StatsLabels[0] := 'Present (' + IntToStr(PanelChart.StatsData[0]) + ')';

  PanelChart.StatsData[1] := Random(50) + 10;
  PanelChart.StatsLabels[1] := 'Work Assignment (' + IntToStr(PanelChart.StatsData[1]) + ')';

  PanelChart.StatsData[2] := Random(30) + 5;
  PanelChart.StatsLabels[2] := 'Medical Assignment (' + IntToStr(PanelChart.StatsData[2]) + ')';

  PanelChart.StatsData[3] := Random(20) + 2;
  PanelChart.StatsLabels[3] := 'Leave (' + IntToStr(PanelChart.StatsData[3]) + ')';

  PanelChart.StatsData[4] := Random(25) + 3;
  PanelChart.StatsLabels[4] := 'Bail Release (' + IntToStr(PanelChart.StatsData[4]) + ')';

  PanelChart.StatsData[5] := Random(15) + 1;
  PanelChart.StatsLabels[5] := 'Other (' + IntToStr(PanelChart.StatsData[5]) + ')';

  PanelChart.Invalidate;
end;

procedure TChartPanel.Paint;
var
  CenterX, CenterY, Radius: Integer;
  StartAngle, SweepAngle: Double;
  Total: Integer;
  i, LegendY: Integer;
  Rect: TRect;
begin
  inherited Paint;

  // حساب المجموع الكلي
  Total := 0;
  for i := 0 to 5 do
    Total := Total + StatsData[i];

  if Total = 0 then
  begin
    Canvas.Font.Size := 16;
    Canvas.Font.Style := [fsBold];
    Canvas.Font.Color := clGray;
    Canvas.TextOut(Width div 2 - 80, Height div 2, 'No Data Available');
    Exit;
  end;

  // رسم العنوان
  Canvas.Font.Size := 18;
  Canvas.Font.Style := [fsBold];
  Canvas.Font.Color := clNavy;
  Canvas.TextOut(Width div 2 - 80, 10, 'Inmate Statistics');

  // حساب مركز ونصف قطر الدائرة
  CenterX := Width div 3;
  CenterY := Height div 2;
  Radius := Min(Width div 4, Height div 3);

  // رسم الرسم البياني الدائري
  StartAngle := 0;
  for i := 0 to 5 do
  begin
    if StatsData[i] > 0 then
    begin
      SweepAngle := (StatsData[i] / Total) * 360;

      Canvas.Brush.Color := StatsColors[i];
      Canvas.Pen.Color := clBlack;
      Canvas.Pen.Width := 2;

      // رسم القطاع
      Canvas.Pie(CenterX - Radius, CenterY - Radius,
                 CenterX + Radius, CenterY + Radius,
                 Round(CenterX + Radius * Cos(DegToRad(StartAngle))),
                 Round(CenterY - Radius * Sin(DegToRad(StartAngle))),
                 Round(CenterX + Radius * Cos(DegToRad(StartAngle + SweepAngle))),
                 Round(CenterY - Radius * Sin(DegToRad(StartAngle + SweepAngle))));

      StartAngle := StartAngle + SweepAngle;
    end;
  end;

  // رسم المفتاح
  Canvas.Font.Size := 12;
  Canvas.Font.Style := [fsBold];
  LegendY := 50;

  for i := 0 to 5 do
  begin
    if StatsData[i] > 0 then
    begin
      Rect.Left := Width - 200;
      Rect.Top := LegendY;
      Rect.Right := Width - 180;
      Rect.Bottom := LegendY + 15;
      Canvas.Brush.Color := StatsColors[i];
      Canvas.FillRect(Rect);
      Canvas.Pen.Color := clBlack;
      Canvas.Rectangle(Rect);

      Canvas.Brush.Style := bsClear;
      Canvas.Font.Color := clBlack;
      Canvas.TextOut(Width - 175, LegendY, StatsLabels[i]);

      LegendY := LegendY + 25;
    end;
  end;
end;

end.
