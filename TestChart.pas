unit TestChart;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls, Math;

type
  TFormTest = class(TForm)
    Panel1: TPanel;
    Button1: TButton;
    PanelChart: TPanel;
    procedure Button1Click(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure PanelChartPaint(Sender: TObject);
  private
    StatsData: array[0..5] of Integer;
    StatsLabels: array[0..5] of string;
    StatsColors: array[0..5] of TColor;
  public
  end;

var
  FormTest: TFormTest;

implementation

{$R *.dfm}

procedure TFormTest.FormCreate(Sender: TObject);
begin
  PanelChart.OnPaint := PanelChartPaint;
  PanelChart.Color := clWhite;
  PanelChart.BevelOuter := bvNone;
end;

procedure TFormTest.Button1Click(Sender: TObject);
begin
  // بيانات تجريبية
  StatsData[0] := 150;
  StatsLabels[0] := 'موجود (150)';
  StatsColors[0] := clGreen;
  
  StatsData[1] := 25;
  StatsLabels[1] := 'مأمورية خروج (25)';
  StatsColors[1] := clBlue;
  
  StatsData[2] := 10;
  StatsLabels[2] := 'مأمورية عيادة (10)';
  StatsColors[2] := clYellow;
  
  StatsData[3] := 5;
  StatsLabels[3] := 'إجازة (5)';
  StatsColors[3] := clOrange;
  
  StatsData[4] := 8;
  StatsLabels[4] := 'خروج بكفالة (8)';
  StatsColors[4] := clRed;
  
  StatsData[5] := 2;
  StatsLabels[5] := 'أخرى (2)';
  StatsColors[5] := clGray;
  
  PanelChart.Invalidate;
end;

procedure TFormTest.PanelChartPaint(Sender: TObject);
var
  Panel: TPanel;
  Canvas: TCanvas;
  CenterX, CenterY, Radius: Integer;
  StartAngle, SweepAngle: Double;
  Total: Integer;
  i, LegendY: Integer;
  Rect: TRect;
begin
  Panel := Sender as TPanel;
  Canvas := Panel.Canvas;
  
  // حساب المجموع الكلي
  Total := 0;
  for i := 0 to 5 do
    Total := Total + StatsData[i];
    
  if Total = 0 then
  begin
    Canvas.Font.Size := 16;
    Canvas.Font.Style := [fsBold];
    Canvas.Font.Color := clGray;
    Canvas.TextOut(Panel.Width div 2 - 80, Panel.Height div 2, 'لا توجد بيانات');
    Exit;
  end;
  
  // رسم العنوان
  Canvas.Font.Size := 18;
  Canvas.Font.Style := [fsBold];
  Canvas.Font.Color := clNavy;
  Canvas.TextOut(Panel.Width div 2 - 80, 10, 'إحصائيات النزلاء');
  
  // حساب مركز ونصف قطر الدائرة
  CenterX := Panel.Width div 3;
  CenterY := Panel.Height div 2;
  Radius := Min(Panel.Width div 4, Panel.Height div 3);
  
  // رسم الرسم البياني الدائري
  StartAngle := 0;
  for i := 0 to 5 do
  begin
    if StatsData[i] > 0 then
    begin
      SweepAngle := (StatsData[i] / Total) * 360;
      
      Canvas.Brush.Color := StatsColors[i];
      Canvas.Pen.Color := clBlack;
      Canvas.Pen.Width := 2;
      
      // رسم القطاع
      Canvas.Pie(CenterX - Radius, CenterY - Radius, 
                 CenterX + Radius, CenterY + Radius,
                 Round(CenterX + Radius * Cos(DegToRad(StartAngle))),
                 Round(CenterY - Radius * Sin(DegToRad(StartAngle))),
                 Round(CenterX + Radius * Cos(DegToRad(StartAngle + SweepAngle))),
                 Round(CenterY - Radius * Sin(DegToRad(StartAngle + SweepAngle))));
      
      StartAngle := StartAngle + SweepAngle;
    end;
  end;
  
  // رسم المفتاح
  Canvas.Font.Size := 12;
  Canvas.Font.Style := [fsBold];
  LegendY := 50;
  
  for i := 0 to 5 do
  begin
    if StatsData[i] > 0 then
    begin
      Rect := Classes.Rect(Panel.Width - 200, LegendY, Panel.Width - 180, LegendY + 15);
      Canvas.Brush.Color := StatsColors[i];
      Canvas.FillRect(Rect);
      Canvas.Pen.Color := clBlack;
      Canvas.Rectangle(Rect);
      
      Canvas.Brush.Style := bsClear;
      Canvas.Font.Color := clBlack;
      Canvas.TextOut(Panel.Width - 175, LegendY, StatsLabels[i]);
      
      LegendY := LegendY + 25;
    end;
  end;
end;

end.
